/**
 * @note
 * errno
 *
 * <AUTHOR>
 * @date 	2025-02-05
 */
package common

import (
	"gitlab.docsl.com/security/common"
)

// 将特定的errno注入ErrnoDesc
func init() {
	for k, v := range ErrnoDesc {
		common.ErrnoDesc[k] = v
	}
}

const (
	ErrTriggerWorkflow = 10000

	// System相关错误码
	ErrQuerySystemList = 20001
	ErrCreateSystem    = 20002
	ErrUpdateSystem    = 20003
	ErrDeleteSystem    = 20004

	// Template相关错误码
	ErrQueryTemplateList = 30001
	ErrCreateTemplate    = 30002
	ErrUpdateTemplate    = 30003
	ErrDeleteTemplate    = 30004

	ErrWebAuthnValidateBegin  = 100000
	ErrWebAuthnValidateFinish = 100001
	ErrWebAuthnSignupBegin    = 100002
	ErrWebAuthnSignupFinish   = 100003
)

var ErrnoDesc = map[int]string{
	ErrTriggerWorkflow: "触发流程失败",

	// System相关错误描述
	ErrQuerySystemList: "查询系统列表失败",
	ErrCreateSystem:    "创建系统失败",
	ErrUpdateSystem:    "更新系统失败",
	ErrDeleteSystem:    "删除系统失败",

	ErrWebAuthnValidateBegin:  "Webauthn令牌校验初始化失败",
	ErrWebAuthnValidateFinish: "Webauthn令牌校验失败",
	ErrWebAuthnSignupBegin:    "Webauthn令牌注册初始化失败",
	ErrWebAuthnSignupFinish:   "Webauthn令牌注册失败",
}
