/**
 * @note
 * system handler
 *
 * <AUTHOR>
 * @date 	2025-07-04
 */
package system

import (
	"github.com/kataras/iris/v12"

	systemLogic "gitlab.docsl.com/security/audit/internal/logic/system"
	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
	. "gitlab.docsl.com/security/common"
)

// ListSystems 查询系统列表
func ListSystems(ctx iris.Context) {
	req := &ListSystemRequest{}
	if err := ctx.ReadQuery(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 构造查询过滤器
	filter := systemModel.QuerySystemFilter{
		Page:       req.Page,
		PerPage:    req.PerPage,
		SystemID:   req.SystemID,
		SystemName: req.SystemName,
		KeyType:    req.KeyType,
		Order:      req.Order,
	}

	// 查询列表
	systems, err := systemModel.QuerySystemsBySeveralConditions(ctx, filter)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrQuerySystemList, err))
		return
	}

	// 查询总数
	total, err := systemModel.QuerySystemsCountBySeveralConditions(ctx, filter)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrQuerySystemList, err))
		return
	}

	// 构造响应
	response := &ListSystemResponse{
		Total: total,
		List:  ConvertToSystemResponseList(systems),
	}

	SetRet(ctx, NewError(ErrCodeOK).SetDesc(response))
}

// CreateSystem 创建系统
func CreateSystem(ctx iris.Context) {
	req := &CreateSystemRequest{}
	if err := ctx.ReadJSON(req); err != nil {
		SetRet(ctx, NewError(ErrCodeJson, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 创建系统
	id, err := systemModel.CreateSystem(ctx, req.SystemID, req.SystemName, req.Description, req.KeyType, req.PublicKey)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrCreateSystem, err))
		return
	}

	response := &CreateSystemResponse{ID: id}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationSystemCreate, &op_log.OperationDetail{
		Target: req,
		After:  response,
	})

	SetRet(ctx, NewError(ErrCodeOK).SetDesc(response))
}

// UpdateSystem 更新系统
func UpdateSystem(ctx iris.Context) {
	req := &UpdateSystemRequest{}
	if err := ctx.ReadJSON(req); err != nil {
		SetRet(ctx, NewError(ErrCodeJson, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 查询原始数据用于日志记录
	originalSystem, err := systemModel.GetSystemByID(ctx, req.ID)
	if err != nil {
		SetRet(ctx, NewErrorWithMessage(auditCommon.ErrUpdateSystem, err.Error()))
		return
	}

	// 更新系统
	err = systemModel.UpdateSystem(ctx, req.ID, req.SystemName, req.Description, req.KeyType, req.PublicKey)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrUpdateSystem, err))
		return
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationSystemUpdate, &op_log.OperationDetail{
		Target: map[string]interface{}{"id": req.ID},
		Before: ConvertToSystemResponse(originalSystem),
		After:  req,
	})

	SetRet(ctx, NewError(ErrCodeOK))
}

// DeleteSystem 删除系统
func DeleteSystem(ctx iris.Context) {
	req := &DeleteSystemRequest{}
	if err := ctx.ReadJSON(req); err != nil {
		SetRet(ctx, NewError(ErrCodeJson, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 查询原始数据用于日志记录
	originalSystem, err := systemModel.GetSystemByID(ctx, req.ID)
	if err != nil {
		SetRet(ctx, NewErrorWithMessage(auditCommon.ErrDeleteSystem, err.Error()))
		return
	}

	// 删除系统
	err = systemModel.DeleteSystem(ctx, req.ID)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrDeleteSystem, err))
		return
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationSystemDelete, &op_log.OperationDetail{
		Target: map[string]interface{}{"id": req.ID},
		Before: ConvertToSystemResponse(originalSystem),
	})

	SetRet(ctx, NewError(ErrCodeOK))
}
