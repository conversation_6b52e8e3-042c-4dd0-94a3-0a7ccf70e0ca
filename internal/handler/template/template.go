/**
 * @note
 * template handler
 *
 * <AUTHOR>
 * @date 	2025-07-04
 */
package template

import (
	"github.com/kataras/iris/v12"

	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
	tLogic "gitlab.docsl.com/security/audit/internal/logic/template"
	. "gitlab.docsl.com/security/common"
)

// QueryWorkflowTemplates 查询流程模板列表
func QueryWorkflowTemplates(ctx iris.Context) {
	req := &QueryWorkflowTemplateRequest{}
	if err := ctx.ReadQuery(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 调用logic层
	logicReq := &tLogic.QueryWorkflowTemplateRequest{
		Page:    req.Page,
		PerPage: req.PerPage,
	}

	response, err := tLogic.QueryWorkflowTemplates(ctx, logicReq)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrQueryTemplateList, err))
		return
	}

	// 转换为handler响应格式
	handlerResponse := &QueryWorkflowTemplateResponse{
		Total: response.Total,
		Items: response.Items,
	}

	SetRet(ctx, NewError(ErrCodeOK).SetDesc(handlerResponse))
}

// CreateWorkflowTemplate 创建流程模板
func CreateWorkflowTemplate(ctx iris.Context) {
	req := &CreateWorkflowTemplateRequest{}
	if err := ctx.ReadJSON(req); err != nil {
		SetRet(ctx, NewError(ErrCodeJson, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 调用logic层
	logicReq := &tLogic.CreateWorkflowTemplateRequest{
		WorkflowName:  req.WorkflowName,
		Description:   req.Description,
		NodeTemplates: req.NodeTemplates,
	}

	response, err := tLogic.CreateWorkflowTemplate(ctx, logicReq)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrCreateTemplate, err))
		return
	}

	// 转换为handler响应格式
	handlerResponse := &CreateWorkflowTemplateResponse{
		WorkflowTemplateID: response.WorkflowTemplateID,
	}

	SetRet(ctx, NewError(ErrCodeOK).SetDesc(handlerResponse))
}

// UpdateWorkflowTemplate 更新流程模板
func UpdateWorkflowTemplate(ctx iris.Context) {
	req := &UpdateWorkflowTemplateRequest{}
	if err := ctx.ReadJSON(req); err != nil {
		SetRet(ctx, NewError(ErrCodeJson, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 调用logic层
	logicReq := &tLogic.UpdateWorkflowTemplateRequest{
		WorkflowName:  req.WorkflowName,
		Description:   req.Description,
		NodeTemplates: req.NodeTemplates,
	}

	response, err := tLogic.UpdateWorkflowTemplate(ctx, logicReq)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrUpdateTemplate, err))
		return
	}

	// 转换为handler响应格式
	handlerResponse := &UpdateWorkflowTemplateResponse{
		WorkflowTemplateID: response.WorkflowTemplateID,
	}

	SetRet(ctx, NewError(ErrCodeOK).SetDesc(handlerResponse))
}

// DeleteWorkflowTemplate 删除流程模板
func DeleteWorkflowTemplate(ctx iris.Context) {
	req := &DeleteWorkflowTemplateRequest{}
	if err := ctx.ReadJSON(req); err != nil {
		SetRet(ctx, NewError(ErrCodeJson, err))
		return
	}

	if err := Validator.Struct(req); err != nil {
		SetRet(ctx, NewError(ErrCodeParam, err))
		return
	}

	// 调用logic层
	logicReq := &tLogic.DeleteWorkflowTemplateRequest{
		WorkflowName: req.WorkflowName,
	}

	err := tLogic.DeleteWorkflowTemplate(ctx, logicReq)
	if err != nil {
		SetRet(ctx, NewError(auditCommon.ErrDeleteTemplate, err))
		return
	}

	SetRet(ctx, NewError(ErrCodeOK))
}
