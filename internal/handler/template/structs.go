/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-07-04
 */
package template

import (
	tLogic "gitlab.docsl.com/security/audit/internal/logic/template"
)

type QueryWorkflowTemplateRequest struct {
	Page    int `json:"page" validate:"gte=0"`
	PerPage int `json:"perPage" validate:"gt=0"`
}

type QueryWorkflowTemplateResponse struct {
	Total int64                          `json:"total"`
	Items []*tLogic.WorkflowTemplateItem `json:"items"`
}

type CreateWorkflowTemplateRequest struct {
	WorkflowName  string                     `json:"workflowName" validate:"required"`
	Description   string                     `json:"description"`
	NodeTemplates []*tLogic.NodeTemplateItem `json:"nodeTemplates" validate:"required"`
}

type UpdateWorkflowTemplateRequest struct {
	WorkflowName  string                     `json:"workflowName" validate:"required"`
	Description   string                     `json:"description"`
	NodeTemplates []*tLogic.NodeTemplateItem `json:"nodeTemplates" validate:"required"`
}
