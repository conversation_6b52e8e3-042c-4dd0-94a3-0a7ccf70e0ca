/**
 * @note
 * template logic
 *
 * <AUTHOR>
 * @date 	2025-07-04
 */
package template

import (
	"context"
	"fmt"

	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
	"gitlab.docsl.com/security/audit/pkg/model/op_log"
	flowModel "gitlab.docsl.com/security/audit/internal/model/flow"
	"gorm.io/gorm"
)

// QueryWorkflowTemplates 查询流程模板列表（只查询is_latest=true的）
func QueryWorkflowTemplates(ctx context.Context, req *QueryWorkflowTemplateRequest) (*QueryWorkflowTemplateResponse, error) {
	// 构造查询过滤器，只查询最新版本
	isLatest := true
	filter := flowModel.QueryWorkflowTemplateFilter{
		Page:     req.Page,
		PerPage:  req.PerPage,
		IsLatest: &isLatest,
		Order:    "created_at DESC",
	}

	// 查询流程模板列表
	workflowTemplates, err := flowModel.QueryWorkflowTemplatesBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 查询总数
	total, err := flowModel.QueryWorkflowTemplatesCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 构造响应
	items := make([]*WorkflowTemplateItem, 0, len(workflowTemplates))
	for _, wt := range workflowTemplates {
		// 查询每个流程模板的节点模板
		templateID := int64(wt.ID)
		nodeTemplateFilter := flowModel.QueryNodeTemplateFilter{
			WorkflowTemplateID: &templateID,
		}
		nodeTemplates, err := flowModel.QueryNodeTemplatesBySeveralConditions(ctx, nodeTemplateFilter)
		if err != nil {
			return nil, err
		}

		// 转换为响应格式
		item := ConvertToWorkflowTemplateItem(wt, nodeTemplates)
		items = append(items, item)
	}

	response := &QueryWorkflowTemplateResponse{
		Total: total,
		Items: items,
	}

	return response, nil
}

// CreateWorkflowTemplate 创建流程模板
func CreateWorkflowTemplate(ctx context.Context, req *CreateWorkflowTemplateRequest) (*CreateWorkflowTemplateResponse, error) {
	var workflowTemplateID int64

	// 在事务中执行创建操作
	err := flowModel.ExecuteInTransaction(ctx, func(tx *gorm.DB) error {
		// 1. 创建流程模板
		nodeCount := int64(len(req.NodeTemplates))
		id, err := flowModel.CreateWorkflowTemplate(ctx, req.WorkflowName, req.Description, nodeCount, true)
		if err != nil {
			return fmt.Errorf("failed to create workflow template: %v", err)
		}
		workflowTemplateID = int64(id)

		// 2. 创建节点模板
		for i, nodeTemplate := range req.NodeTemplates {
			_, err := flowModel.CreateNodeTemplate(ctx, 
				nodeTemplate.NodeName, 
				workflowTemplateID, 
				nodeTemplate.Description, 
				int64(i), // nodeIndex从0开始
				nodeTemplate.SystemID, 
				nodeTemplate.Config)
			if err != nil {
				return fmt.Errorf("failed to create node template %s: %v", nodeTemplate.NodeName, err)
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	response := &CreateWorkflowTemplateResponse{
		WorkflowTemplateID: workflowTemplateID,
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationTemplateCreate, &op_log.OperationDetail{
		Target: req,
		After:  response,
	})

	return response, nil
}

// UpdateWorkflowTemplate 更新流程模板（实际上是创建新版本）
func UpdateWorkflowTemplate(ctx context.Context, req *UpdateWorkflowTemplateRequest) (*UpdateWorkflowTemplateResponse, error) {
	var newWorkflowTemplateID int64

	// 在事务中执行更新操作
	err := flowModel.ExecuteInTransaction(ctx, func(tx *gorm.DB) error {
		// 1. 将同名的旧版本模板的is_latest设为false
		oldTemplates, err := flowModel.QueryWorkflowTemplatesBySeveralConditions(ctx, flowModel.QueryWorkflowTemplateFilter{
			WorkflowName: req.WorkflowName,
		})
		if err != nil {
			return fmt.Errorf("failed to query existing templates: %v", err)
		}

		for _, oldTemplate := range oldTemplates {
			isLatest := false
			err := flowModel.UpdateWorkflowTemplate(ctx, oldTemplate.ID, nil, nil, &isLatest)
			if err != nil {
				return fmt.Errorf("failed to update old template is_latest: %v", err)
			}
		}

		// 2. 创建新的流程模板
		nodeCount := int64(len(req.NodeTemplates))
		description := ""
		if req.Description != nil {
			description = *req.Description
		}
		
		id, err := flowModel.CreateWorkflowTemplate(ctx, req.WorkflowName, description, nodeCount, true)
		if err != nil {
			return fmt.Errorf("failed to create new workflow template: %v", err)
		}
		newWorkflowTemplateID = int64(id)

		// 3. 创建新的节点模板
		for i, nodeTemplate := range req.NodeTemplates {
			_, err := flowModel.CreateNodeTemplate(ctx, 
				nodeTemplate.NodeName, 
				newWorkflowTemplateID, 
				nodeTemplate.Description, 
				int64(i), // nodeIndex从0开始
				nodeTemplate.SystemID, 
				nodeTemplate.Config)
			if err != nil {
				return fmt.Errorf("failed to create node template %s: %v", nodeTemplate.NodeName, err)
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	response := &UpdateWorkflowTemplateResponse{
		WorkflowTemplateID: newWorkflowTemplateID,
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationTemplateUpdate, &op_log.OperationDetail{
		Target: map[string]interface{}{"workflowName": req.WorkflowName},
		After:  response,
	})

	return response, nil
}

// DeleteWorkflowTemplate 删除流程模板（实际上是将is_latest设为false）
func DeleteWorkflowTemplate(ctx context.Context, req *DeleteWorkflowTemplateRequest) error {
	// 查询要删除的模板
	templates, err := flowModel.QueryWorkflowTemplatesBySeveralConditions(ctx, flowModel.QueryWorkflowTemplateFilter{
		WorkflowName: req.WorkflowName,
	})
	if err != nil {
		return fmt.Errorf("failed to query templates: %v", err)
	}

	if len(templates) == 0 {
		return fmt.Errorf("template with name '%s' not found", req.WorkflowName)
	}

	// 记录删除前的状态用于日志
	var beforeData []*WorkflowTemplateItem
	for _, template := range templates {
		if template.IsLatest {
			templateID := int64(template.ID)
			nodeTemplateFilter := flowModel.QueryNodeTemplateFilter{
				WorkflowTemplateID: &templateID,
			}
			nodeTemplates, err := flowModel.QueryNodeTemplatesBySeveralConditions(ctx, nodeTemplateFilter)
			if err != nil {
				return fmt.Errorf("failed to query node templates: %v", err)
			}
			beforeData = append(beforeData, ConvertToWorkflowTemplateItem(template, nodeTemplates))
		}
	}

	// 将所有同名模板的is_latest设为false
	for _, template := range templates {
		isLatest := false
		err := flowModel.UpdateWorkflowTemplate(ctx, template.ID, nil, nil, &isLatest)
		if err != nil {
			return fmt.Errorf("failed to update template is_latest: %v", err)
		}
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationTemplateDelete, &op_log.OperationDetail{
		Target: map[string]interface{}{"workflowName": req.WorkflowName},
		Before: beforeData,
	})

	return nil
}

// QueryWorkflowTemplateRequest 查询流程模板请求
type QueryWorkflowTemplateRequest struct {
	Page    int `json:"page" validate:"gte=0"`
	PerPage int `json:"perPage" validate:"gt=0"`
}

// QueryWorkflowTemplateResponse 查询流程模板响应
type QueryWorkflowTemplateResponse struct {
	Total int64                  `json:"total"`
	Items []*WorkflowTemplateItem `json:"items"`
}

// CreateWorkflowTemplateRequest 创建流程模板请求
type CreateWorkflowTemplateRequest struct {
	WorkflowName  string              `json:"workflowName" validate:"required"`
	Description   string              `json:"description"`
	NodeTemplates []*NodeTemplateItem `json:"nodeTemplates" validate:"required"`
}

// CreateWorkflowTemplateResponse 创建流程模板响应
type CreateWorkflowTemplateResponse struct {
	WorkflowTemplateID int64 `json:"workflowTemplateID"`
}

// UpdateWorkflowTemplateRequest 更新流程模板请求
type UpdateWorkflowTemplateRequest struct {
	WorkflowName  string              `json:"workflowName" validate:"required"`
	Description   *string             `json:"description"`
	NodeTemplates []*NodeTemplateItem `json:"nodeTemplates" validate:"required"`
}

// UpdateWorkflowTemplateResponse 更新流程模板响应
type UpdateWorkflowTemplateResponse struct {
	WorkflowTemplateID int64 `json:"workflowTemplateID"`
}

// DeleteWorkflowTemplateRequest 删除流程模板请求
type DeleteWorkflowTemplateRequest struct {
	WorkflowName string `json:"workflowName" validate:"required"`
}

// ConvertToWorkflowTemplateItem 转换为WorkflowTemplateItem
func ConvertToWorkflowTemplateItem(wt *flowModel.AuditWorkflowTemplateTable, nodeTemplates []*flowModel.AuditNodeTemplateTable) *WorkflowTemplateItem {
	nodeItems := make([]*NodeTemplateItem, len(nodeTemplates))
	for i, nt := range nodeTemplates {
		nodeItems[i] = &NodeTemplateItem{
			NodeTemplateID: int64(nt.ID),
			NodeName:       nt.NodeName,
			Description:    nt.Description,
			NodeIndex:      nt.NodeIndex,
			SystemID:       nt.SystemID,
			Config:         nt.Config,
		}
	}

	return &WorkflowTemplateItem{
		WorkflowTemplateID: int64(wt.ID),
		WorkflowName:       wt.WorkflowName,
		Description:        wt.Description,
		CreatedAt:          wt.CreatedAt,
		UpdatedAt:          wt.UpdatedAt,
		NodeTemplates:      nodeItems,
	}
}
