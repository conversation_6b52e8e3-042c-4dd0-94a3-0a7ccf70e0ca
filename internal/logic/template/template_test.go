package template

import (
	"testing"

	flowModel "gitlab.docsl.com/security/audit/internal/model/flow"
)

func TestConvertToWorkflowTemplateItem(t *testing.T) {
	// 测试转换函数
	wt := &flowModel.AuditWorkflowTemplateTable{
		WorkflowName: "test-workflow",
		Description:  "Test workflow description",
		NodeCount:    2,
		IsLatest:     true,
	}
	wt.ID = 1

	nodeTemplates := []*flowModel.AuditNodeTemplateTable{
		{
			NodeName:           "node1",
			WorkflowTemplateID: 1,
			Description:        "First node",
			NodeIndex:          0,
			SystemID:           "system1",
			Config:             &flowModel.NodeConfig{},
		},
		{
			NodeName:           "node2",
			WorkflowTemplateID: 1,
			Description:        "Second node",
			NodeIndex:          1,
			SystemID:           "system2",
			Config:             &flowModel.NodeConfig{},
		},
	}
	nodeTemplates[0].ID = 1
	nodeTemplates[1].ID = 2

	result := ConvertToWorkflowTemplateItem(wt, nodeTemplates)

	if result.WorkflowTemplateID != 1 {
		t.<PERSON>rrorf("Expected WorkflowTemplateID 1, got %d", result.WorkflowTemplateID)
	}

	if result.WorkflowName != "test-workflow" {
		t.Errorf("Expected WorkflowName 'test-workflow', got %s", result.WorkflowName)
	}

	if len(result.NodeTemplates) != 2 {
		t.Errorf("Expected 2 node templates, got %d", len(result.NodeTemplates))
	}

	if result.NodeTemplates[0].NodeName != "node1" {
		t.Errorf("Expected first node name 'node1', got %s", result.NodeTemplates[0].NodeName)
	}

	if result.NodeTemplates[1].NodeIndex != 1 {
		t.Errorf("Expected second node index 1, got %d", result.NodeTemplates[1].NodeIndex)
	}
}

func TestQueryWorkflowTemplateRequest(t *testing.T) {
	// 测试请求结构体
	req := &QueryWorkflowTemplateRequest{
		Page:    1,
		PerPage: 10,
	}

	if req.Page != 1 {
		t.Errorf("Expected Page 1, got %d", req.Page)
	}

	if req.PerPage != 10 {
		t.Errorf("Expected PerPage 10, got %d", req.PerPage)
	}
}

func TestCreateWorkflowTemplateRequest(t *testing.T) {
	// 测试创建请求结构体
	req := &CreateWorkflowTemplateRequest{
		WorkflowName: "test-workflow",
		Description:  "Test description",
		NodeTemplates: []*NodeTemplateItem{
			{
				NodeName:    "node1",
				Description: "First node",
				NodeIndex:   0,
				SystemID:    "system1",
				Config:      &flowModel.NodeConfig{},
			},
		},
	}

	if req.WorkflowName != "test-workflow" {
		t.Errorf("Expected WorkflowName 'test-workflow', got %s", req.WorkflowName)
	}

	if len(req.NodeTemplates) != 1 {
		t.Errorf("Expected 1 node template, got %d", len(req.NodeTemplates))
	}
}

func TestUpdateWorkflowTemplateRequest(t *testing.T) {
	// 测试更新请求结构体
	description := "Updated description"
	req := &UpdateWorkflowTemplateRequest{
		WorkflowName: "test-workflow",
		Description:  &description,
		NodeTemplates: []*NodeTemplateItem{
			{
				NodeName:    "node1",
				Description: "Updated node",
				NodeIndex:   0,
				SystemID:    "system1",
				Config:      &flowModel.NodeConfig{},
			},
		},
	}

	if req.WorkflowName != "test-workflow" {
		t.Errorf("Expected WorkflowName 'test-workflow', got %s", req.WorkflowName)
	}

	if req.Description == nil || *req.Description != "Updated description" {
		t.Errorf("Expected Description 'Updated description', got %v", req.Description)
	}
}

func TestDeleteWorkflowTemplateRequest(t *testing.T) {
	// 测试删除请求结构体
	req := &DeleteWorkflowTemplateRequest{
		WorkflowName: "test-workflow",
	}

	if req.WorkflowName != "test-workflow" {
		t.Errorf("Expected WorkflowName 'test-workflow', got %s", req.WorkflowName)
	}
}
