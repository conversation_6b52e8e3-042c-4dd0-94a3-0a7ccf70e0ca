/**
 * @note
 * system logic structs
 *
 * <AUTHOR>
 * @date 	2025-07-04
 */
package system

import (
	"time"

	systemModel "gitlab.docsl.com/security/audit/pkg/model/system"
)

// SystemItem 系统响应结构
type SystemItem struct {
	ID          uint      `json:"id"`
	SystemID    string    `json:"systemID"`
	SystemName  string    `json:"systemName"`
	Description string    `json:"description"`
	KeyType     string    `json:"keyType"`
	PublicKey   string    `json:"publicKey"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// ConvertToSystemItem 转换为响应格式
func ConvertToSystemItem(system *systemModel.AuditSystemTable) *SystemItem {
	return &SystemItem{
		ID:          system.ID,
		SystemID:    system.SystemID,
		SystemName:  system.SystemName,
		Description: system.Description,
		KeyType:     system.KeyType,
		PublicKey:   system.PublicKey,
		CreatedAt:   system.CreatedAt,
		UpdatedAt:   system.UpdatedAt,
	}
}

// ConvertToSystemItemList 转换为响应列表格式
func ConvertToSystemItemList(systems []*systemModel.AuditSystemTable) []*SystemItem {
	result := make([]*SystemItem, len(systems))
	for i, system := range systems {
		result[i] = ConvertToSystemItem(system)
	}
	return result
}
