/**
 * @note
 * system logic
 *
 * <AUTHOR>
 * @date 	2025-07-04
 */
package system

import (
	"context"

	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
	"gitlab.docsl.com/security/audit/pkg/model/op_log"
	systemModel "gitlab.docsl.com/security/audit/pkg/model/system"
)

// ListSystems 查询系统列表
func ListSystems(ctx context.Context, page int, perPage int, systemID string,
	systemName string, keyType string, order string) ([]*SystemItem, int64, error) {
	// 构造查询过滤器
	filter := systemModel.QuerySystemFilter{
		Page:       page,
		PerPage:    perPage,
		SystemID:   systemID,
		SystemName: systemName,
		KeyType:    keyType,
		Order:      order,
	}

	// 查询列表
	systems, err := systemModel.QuerySystemsBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// 查询总数
	total, err := systemModel.QuerySystemsCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	return ConvertToSystemItemList(systems), total, nil
}

// CreateSystem 创建系统
func CreateSystem(ctx context.Context, systemID string, systemName string,
	description string, keyType string, publicKey string) (uint, error) {
	// 创建系统
	id, err := systemModel.CreateSystem(ctx, systemID, systemName, description, keyType, publicKey)
	if err != nil {
		return 0, err
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationSystemCreate, &op_log.OperationDetail{
		Target: systemID,
		After: SystemItem{
			ID:          id,
			SystemID:    systemID,
			SystemName:  systemName,
			Description: description,
			KeyType:     keyType,
			PublicKey:   publicKey,
		},
	})

	return id, nil
}

// UpdateSystem 更新系统
func UpdateSystem(ctx context.Context, systemID string, systemName *string, description *string, keyType *string, publicKey *string) error {
	// 查询原始数据用于日志记录
	originalSystem, err := systemModel.GetSystemBySystemID(ctx, systemID)
	if err != nil {
		return err
	}

	// 更新系统
	err = systemModel.UpdateSystem(ctx, systemID, systemName, description, keyType, publicKey)
	if err != nil {
		return err
	}

	// 记录操作日志
	f := func(v *string) string {
		if v == nil {
			return ""
		}
		return *v
	}
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationSystemUpdate, &op_log.OperationDetail{
		Target: map[string]interface{}{"system_id": systemID},
		Before: ConvertToSystemItem(originalSystem),
		After: SystemItem{
			SystemID:    systemID,
			SystemName:  f(systemName),
			Description: f(description),
			KeyType:     f(keyType),
			PublicKey:   f(publicKey),
		},
	})

	return nil
}

// DeleteSystem 删除系统
func DeleteSystem(ctx context.Context, req *DeleteSystemRequest) error {
	// 查询原始数据用于日志记录
	originalSystem, err := systemModel.GetSystemByID(ctx, req.ID)
	if err != nil {
		return err
	}

	// 删除系统
	err = systemModel.DeleteSystem(ctx, req.ID)
	if err != nil {
		return err
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationSystemDelete, &op_log.OperationDetail{
		Target: map[string]interface{}{"id": req.ID},
		Before: ConvertToSystemItem(originalSystem),
	})

	return nil
}
