/**
 * @note
 * system logic
 *
 * <AUTHOR>
 * @date 	2025-07-04
 */
package system

import (
	"context"

	auditCommon "gitlab.docsl.com/security/audit/pkg/common"
	"gitlab.docsl.com/security/audit/pkg/model/op_log"
	systemModel "gitlab.docsl.com/security/audit/pkg/model/system"
	. "gitlab.docsl.com/security/common"
)

// ListSystems 查询系统列表
func ListSystems(ctx context.Context, req *ListSystemRequest) (*ListSystemResponse, error) {
	// 构造查询过滤器
	filter := systemModel.QuerySystemFilter{
		Page:       req.Page,
		PerPage:    req.PerPage,
		SystemID:   req.SystemID,
		SystemName: req.SystemName,
		KeyType:    req.KeyType,
		Order:      req.Order,
	}

	// 查询列表
	systems, err := systemModel.QuerySystemsBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 查询总数
	total, err := systemModel.QuerySystemsCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 构造响应
	response := &ListSystemResponse{
		Total: total,
		List:  ConvertToSystemResponseList(systems),
	}

	return response, nil
}

// CreateSystem 创建系统
func CreateSystem(ctx context.Context, req *CreateSystemRequest) (*CreateSystemResponse, error) {
	// 创建系统
	id, err := systemModel.CreateSystem(ctx, req.SystemID, req.SystemName, req.Description, req.KeyType, req.PublicKey)
	if err != nil {
		return nil, err
	}

	response := &CreateSystemResponse{ID: id}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationSystemCreate, &op_log.OperationDetail{
		Target: req,
		After:  response,
	})

	return response, nil
}

// UpdateSystem 更新系统
func UpdateSystem(ctx context.Context, req *UpdateSystemRequest) error {
	// 查询原始数据用于日志记录
	originalSystem, err := systemModel.GetSystemByID(ctx, req.ID)
	if err != nil {
		return err
	}

	// 更新系统
	err = systemModel.UpdateSystem(ctx, req.ID, req.SystemName, req.Description, req.KeyType, req.PublicKey)
	if err != nil {
		return err
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationSystemUpdate, &op_log.OperationDetail{
		Target: map[string]interface{}{"id": req.ID},
		Before: ConvertToSystemResponse(originalSystem),
		After:  req,
	})

	return nil
}

// DeleteSystem 删除系统
func DeleteSystem(ctx context.Context, req *DeleteSystemRequest) error {
	// 查询原始数据用于日志记录
	originalSystem, err := systemModel.GetSystemByID(ctx, req.ID)
	if err != nil {
		return err
	}

	// 删除系统
	err = systemModel.DeleteSystem(ctx, req.ID)
	if err != nil {
		return err
	}

	// 记录操作日志
	_ = op_log.CreateOperationLog(ctx, auditCommon.OperationSystemDelete, &op_log.OperationDetail{
		Target: map[string]interface{}{"id": req.ID},
		Before: ConvertToSystemResponse(originalSystem),
	})

	return nil
}
